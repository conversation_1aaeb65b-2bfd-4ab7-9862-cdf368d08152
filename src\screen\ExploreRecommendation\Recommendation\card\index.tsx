import { MapPointIcon, CalendarIcon } from '@/components/icons';
import { Card, CardBody, Button } from '@heroui/react';
import { useState, useRef } from 'react';
import Lightbox from 'yet-another-react-lightbox';
import Thumbnails from 'yet-another-react-lightbox/plugins/thumbnails';
import Video from 'yet-another-react-lightbox/plugins/video';
import 'yet-another-react-lightbox/styles.css';
import 'yet-another-react-lightbox/plugins/thumbnails.css';
import './lightbox-custom.css';

import Image from 'next/image';
import Link from 'next/link';

interface Recommendation {
  title: string;
  duration: string;
  location: string;
  tags: string;
  media: {
    large: string;
    small: string[];
  };
  badge: string;
}

interface RecommendationCardProps {
  recommendations: Recommendation;
}
/**
 * Enhanced RecommendationCard with Gallery Features
 *
 * Features:
 * - Bottom thumbnail gallery with clickable navigation
 * - Support for both images and videos
 * - Blurred background instead of solid black
 * - Scrollable thumbnails for many images
 * - Active thumbnail highlighting
 * - Direct navigation by clicking on grid images
 * - Smooth animations and transitions
 */
const RecommendationCard = ({ recommendations }: RecommendationCardProps) => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const thumbnailsRef = useRef(null);

  // Helper function to detect if a URL is a video
  const isVideoUrl = (url: string): boolean => {
    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi', '.mkv', '.m4v'];
    const videoHosts = ['gtv-videos-bucket', 'youtube.com', 'vimeo.com', 'sample-videos.com'];

    return videoExtensions.some(ext => url.toLowerCase().includes(ext.toLowerCase())) ||
           videoHosts.some(host => url.toLowerCase().includes(host));
  };

  // Component to render media (image or video thumbnail)
  const MediaThumbnail = ({
    src,
    alt,
    width,
    height,
    className
  }: {
    src: string;
    alt: string;
    width: number;
    height: number;
    className: string;
  }) => {
    if (isVideoUrl(src)) {
      // Determine play button size based on thumbnail size
      const isSmallThumbnail = width <= 53;
      const playButtonSize = isSmallThumbnail ? 'w-4 h-4' : 'w-8 h-8';
      const playIconSize = isSmallThumbnail ? 'text-xs' : 'text-sm';

      return (
        <div
          className={`${className} bg-gray-100 flex items-center justify-center relative overflow-hidden`}
        >
          <video
            src={src}
            className="w-full h-full object-cover"
            preload="metadata"
            muted
            playsInline
          />
          <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
            <div className={`${playButtonSize} bg-white bg-opacity-80 rounded-full flex items-center justify-center`}>
              <span className={`text-black ${playIconSize}`}>▶</span>
            </div>
          </div>
        </div>
      );
    }

    return (
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={className}
      />
    );
  };

  // Helper function to create slide object for lightbox
  const createSlide = (src: string, alt: string) => {
    if (isVideoUrl(src)) {
      return {
        type: 'video' as const,
        src, // Video plugin expects src property
        sources: [
          {
            src,
            type: src.toLowerCase().includes('.webm') ? 'video/webm' :
                  src.toLowerCase().includes('.ogg') ? 'video/ogg' : 'video/mp4',
          },
        ],
        alt,
        // For videos, try to use a poster image or fallback to video URL
        poster: src.replace(/\.(mp4|webm|ogg|mov|avi|mkv|m4v)$/i, '.jpg'),
        thumbnail: src, // Use video URL as thumbnail for now
        width: 1920,
        height: 1080,
      };
    }
    return {
      src,
      alt,
      width: 1920,
      height: 1080,
    };
  };

  // Create slides array for lightbox from recommendation media
  const slides = [
    createSlide(recommendations.media.large, 'Large Image'),
    ...recommendations.media.small.map((src, index) =>
      createSlide(src, `Small Image ${index + 1}`)
    ),
  ];

  // Handle opening lightbox from a specific image in the grid
  const handleImageClick = (e: React.MouseEvent, index: number) => {
    e.preventDefault();
    e.stopPropagation();
    setLightboxIndex(index);
    setLightboxOpen(true);
  };

  return (
    <div className="flex flex-col gap-2 flex-1 overflow-hidden mb-3">
      <Card
        key={`${recommendations.title}-${recommendations.location}-${recommendations.duration}`}
        className="bg-white border-none shadow-none hover:border-black hover:!bg-white transition-all duration-300 ease-in-out flex-shrink-0"
        isHoverable
        isPressable
        data-card="recommendation" // Used for DOM measurements
      >
        <CardBody>
          <div className="flex flex-row max-md:flex-col justify-between rounded-xl cursor-pointer w-full overflow-hidden recommendation-card-container">
            {/* Left section: Image and details */}
            <div className="flex flex-row max-md:flex-col gap-4 min-w-0 flex-1">
              {/* grid Image  */}

              <div className="flex flex-col gap-1.5 w-full max-w-[230px] min-w-0 recommendation-image-grid">
                {/* Large Image */}
                <div
                  className="w-full cursor-pointer flex-shrink-0"
                  onClick={(e) => handleImageClick(e, 0)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      e.stopPropagation();
                      setLightboxIndex(0);
                      setLightboxOpen(true);
                    }
                  }}
                  role="button"
                  tabIndex={0}
                  aria-label="View large image in gallery"
                >
                  <MediaThumbnail
                    src={recommendations.media.large}
                    alt="Large Image"
                    width={230}
                    height={120}
                    className="w-full h-[120px] object-cover rounded-tl-xl rounded-tr-xl hover:opacity-90 transition-opacity"
                  />
                </div>

                {/* Small Image Grid - Fixed 53x53 size */}
                <div className="grid grid-cols-4 gap-1.5 w-full recommendation-small-grid">
                  {recommendations.media.small.slice(0, 3).map((src, index) => (
                    <div
                      key={index}
                      className="cursor-pointer w-[53px] h-[53px] flex-shrink-0 recommendation-small-thumbnail"
                      onClick={(e) => handleImageClick(e, index + 1)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          e.stopPropagation();
                          setLightboxIndex(index + 1);
                          setLightboxOpen(true);
                        }
                      }}
                      role="button"
                      tabIndex={0}
                      aria-label={`View small image ${index + 1} in gallery`}
                    >
                      <MediaThumbnail
                        src={src}
                        alt={`Small Image ${index + 1}`}
                        width={53}
                        height={53}
                        className="w-[53px] h-[53px] object-cover hover:opacity-90 transition-opacity rounded-sm"
                      />
                    </div>
                  ))}

                  {/* Blurred Last Image with Overlay Text - Fixed 53x53 size */}
                  <div
                    className="relative cursor-pointer w-[53px] h-[53px] flex-shrink-0 recommendation-small-thumbnail"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      // Start from the 4th image (index 4) if it exists, otherwise start from beginning
                      const startIndex = recommendations.media.small.length > 3 ? 4 : 0;
                      setLightboxIndex(startIndex);
                      setLightboxOpen(true);
                    }}
                    onKeyDown={e => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        e.stopPropagation();
                        const startIndex = recommendations.media.small.length > 3 ? 4 : 0;
                        setLightboxIndex(startIndex);
                        setLightboxOpen(true);
                      }
                    }}
                    role="button"
                    tabIndex={0}
                    aria-label={`View all ${slides.length} images in gallery`}
                  >
                    <MediaThumbnail
                      src={recommendations.media.small[3]}
                      alt="Small Image 4 (Blurred)"
                      width={53}
                      height={53}
                      className="w-[53px] h-[53px] object-cover rounded-sm blur-xs"
                    />
                    <div className="absolute inset-0 flex items-center justify-center hover:bg-black/20 transition-colors rounded-sm">
                      <span className="text-white text-xs font-medium leading-tight text-center">
                        View All<br />({slides.length})
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              {/* Text content */}
              <div className="text-sm space-y-0.5 min-w-0 flex-1">
                <p className="font-medium text-lg">{recommendations.title}</p>
                <div className="flex flex-row gap-3 py-2">
                  <div className="flex flex-row gap-1 items-center">
                    <CalendarIcon
                      size={14}
                      isAnimation={false}
                      className="text-black"
                    />
                    <p className=" text-md">{recommendations.duration}</p>
                  </div>

                  <div className="flex flex-row gap-1 items-center">
                    <MapPointIcon
                      size={14}
                      isAnimation={false}
                      className="text-black"
                    />
                    <p className=" text-md">{recommendations.location}</p>
                  </div>
                </div>
                <div>
                  <p className="text-default-700 text-md">
                    Book with only 30% of total payment
                  </p>
                  <p className="text-default-700 text-md">
                    Customize free of cost
                  </p>
                </div>

                <p className="text-md font-medium py-2">
                  Activities, Explore, Leisure, Family
                </p>

                <p className="text-md text-default-700">
                  Explore Berlin's historic neighborhoods and vibrant street
                  art.
                </p>
              </div>
            </div>
            {/* Right section: Action button */}
            <div className="flex flex-col gap-2 justify-between text-right">
              <p className="text-base font-bold">
                4.2{' '}
                <span className="text-default-700 font-medium">
                  (103 Ratings)
                </span>
              </p>
              <div className="text-right">
                <div className="">
                  <span className="text-default-700 line-through text-md">
                    $3,500
                  </span>
                  <span className="font-bold ml-3 text-xl">$2,999</span>
                </div>
                <div className="flex justify-end py-2">
                  <p className="text-default-700 text-md w-[130px] text-right">
                    $150 taxes & fees Per Person
                  </p>
                </div>
                <Link
                  href={`/explore/${recommendations.location
                    .toLowerCase()
                    .replace(/\s/g, '-')}/${recommendations.title
                    .toLowerCase()
                    .replace(/\s/g, '-')}`}
                  // className="px-5 py-2 rounded-full bg-primary text-white text-sm font-semibold hover:opacity-90 transition text-center"
                >
                  <Button
                    color="primary"
                    // variant="bordered"
                    size="sm"
                    className="font-semibold  rounded-full bg-primary-200 text-white"
                  >
                    Book Trip
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Enhanced Lightbox Component with Gallery Features */}
      <Lightbox
        open={lightboxOpen}
        close={() => setLightboxOpen(false)}
        slides={slides}
        index={lightboxIndex}
        plugins={[Thumbnails, Video]}
        thumbnails={{
          ref: thumbnailsRef,
          position: 'bottom',
          width: 100,
          height: 70,
          border: 2,
          borderRadius: 12,
          padding: 6,
          gap: 16,
          imageFit: 'cover',
          vignette: true,
          showToggle: false,
        }}

        video={{
          controls: true,
          playsInline: true,
          preload: 'metadata',
        }}
        carousel={{
          finite: false,
          preload: 2,
          spacing: '20px',
        }}
        animation={{
          fade: 400,
          swipe: 600,
          easing: {
            fade: 'cubic-bezier(0.4, 0, 0.2, 1)',
            swipe: 'cubic-bezier(0.4, 0, 0.2, 1)',
          },
        }}
        controller={{
          closeOnBackdropClick: true,
          closeOnPullDown: true,
          closeOnPullUp: true,
        }}
        styles={{
          container: {
            backgroundColor: 'rgba(255, 255, 255, 0.15)',
            backdropFilter: 'blur(20px) saturate(180%)',
          },
          thumbnailsContainer: {
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(15px) saturate(150%)',
          },
        }}
        on={{
          view: ({ index }) => setLightboxIndex(index),
        }}
      />
    </div>
  );
};

export default RecommendationCard;
